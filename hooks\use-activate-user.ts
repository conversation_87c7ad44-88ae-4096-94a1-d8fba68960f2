import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { toast } from 'sonner'
import { DEFAULT_API_URL } from '@/constants/constants'

export function useActivateUser() {
    const { data: session } = useSession()
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)

    // TODO: Arreglar CORS PATCH
    const activateOrDeactivateUser = async (userId: string, isActive: boolean) => {
        if (!session?.djangoAccessToken) {
            throw new Error('Autenticació requerida. Torna a iniciar sessió.')
        }

        setLoading(true)
        setError(null)

        try {
            const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL


            const headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${session.djangoAccessToken}`
            }

            const response = await fetch(`${backendUrl}/users/${userId}/activate_or_deactivate/`, {
                headers,
                method: 'PATCH',
                body: JSON.stringify({ is_active: isActive }),
            })

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}))
                throw new Error(errorData.message || `No s'ha pogut actualitzar l'usuari: ${response.statusText}`)
            }

            toast.success('Usuari actualitzat correctament!')
        } catch (err: any) {
            console.error('Error activating/deactivating user:', err)
            setError(err.message || 'S\'ha produït un error desconegut.')
            toast.error(err.message || 'S\'ha produït un error desconegut.')
        } finally {
            setLoading(false)
        }
    }

    return { activateOrDeactivateUser, loading, error }
}